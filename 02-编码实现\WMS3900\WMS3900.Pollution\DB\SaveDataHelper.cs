using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Fpi.Data.Config;
using Fpi.DB;
using Fpi.DB.Manager;
using Fpi.DB.SqlUtil;
using Fpi.HB.Business.HisData;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3900.Pollution.DB
{
    /// <summary>
    /// 存储数据帮助类
    /// </summary>
    public static class SaveDataHelper
    {
        #region 属性字段

        private static readonly object lockObj = new object();

        #endregion

        #region 公共方法

        /// <summary>
        /// 写污染源数据到数据库
        /// </summary>
        public static void WritePollutionDataToDb(DateTime time, ePollutionDataType dataType)
        {
            lock(lockObj)
            {
                _savePollutionData(time, dataType);
            }
        }

        /// <summary>
        /// 写污染源数据到数据库
        /// </summary>
        /// <param name="time"></param>
        private static void _savePollutionData(DateTime time, ePollutionDataType dataType)
        {
            // 检查数据是否已存在
            if(IsDataExists(time, dataType))
            {
                return;
            }

            // 不同类型数据，采用不同的存储方式
            switch(dataType)
            {
                case ePollutionDataType.实时数据:
                    // 插入30秒数据
                    InsertSecondPollutionData(time);
                    break;
                case ePollutionDataType.分钟数据:
                case ePollutionDataType.十分钟数据:
                case ePollutionDataType.小时数据:
                case ePollutionDataType.日数据:
                    // 插入计算后的数据
                    InsertCalculatedPollutionData(time, dataType);
                    break;
            }
        }

        #endregion

        #region 数据计算

        #region 30秒数据

        /// <summary>
        /// 插入30秒数据（实时数据）
        /// </summary>
        /// <param name="time"></param>
        /// <param name="dataType"></param>
        private static void InsertSecondPollutionData(DateTime time)
        {
            int dataType = (int)ePollutionDataType.实时数据;

            // 获取所有待存储的因子
            var valueNodeList = ReportManager.GetInstance().GetFirstQueryGroup()?.GetQueryGroupValueNode();
            if(valueNodeList == null || valueNodeList.Count == 0) return;

            // 累计流量因子ID
            var totalFlowNodeId = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId;
            // 当前累计流量值
            double currenTotalFlowValue = double.NaN;

            var sb = new StringBuilder();
            sb.Append("insert into ").Append(DbConfig.POLLUTION_MEASURE_DATA_TABLE).Append($"(datatime,datatype");

            // 各统计量因子
            QueryGroup queryGroup = ReportManager.GetInstance().GetFirstQueryGroup();
            if(queryGroup != null)
            {
                // 各测量点及数据标志字段名
                foreach(var valueNode in valueNodeList)
                {
                    string valueNodeId = valueNode.id;
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId);
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId).Append(DbConfig.POSTFIX);
                }
            }

            sb.Append($",{DbConfig.TOTAL_FLOW}) values('").Append(time.ToString(DbConfig.DATETIME_FORMAT)).Append("', ").Append(dataType);

            // 各测量点及数据标志值
            foreach(var valueNode in valueNodeList)
            {
                var value = valueNode.GetValue();
                sb.Append(", ").Append(value.ToDBSaveFormat());
                sb.Append(", ").Append(valueNode.State);

                // 记录累计流量因子当前值
                if(totalFlowNodeId == valueNode.id)
                {
                    currenTotalFlowValue = value;
                }
            }

            double totalFlowValue = double.NaN;

            // 计算时段内累计流量差值
            if(!double.IsNaN(currenTotalFlowValue))
            {
                totalFlowValue = CalculateTotalFlowDifference(time, totalFlowNodeId, currenTotalFlowValue);
            }

            sb.Append(", ").Append(totalFlowValue.ToDBSaveFormat()).Append(")");

            DbAccess.ExecuteNonQuery(sb.ToString());
        }

        /// <summary>
        /// 计算30秒数据中，当前组与上一组累计流量差值
        /// </summary>
        /// <param name="time">当前时间</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="totalFlowNodeId">累计流量因子ID</param>
        /// <param name="totalFlowNodeId">当前累计流量</param>
        /// <returns>累计流量差值</returns>
        private static double CalculateTotalFlowDifference(DateTime time, string totalFlowNodeId, double currenTotalFlowValue)
        {
            try
            {
                // 如果累计流量因子ID为空，或者当前累计流量值无效，返回0
                if(string.IsNullOrEmpty(totalFlowNodeId) || double.IsNaN(currenTotalFlowValue) || double.IsInfinity(currenTotalFlowValue))
                {
                    return 0;
                }

                // 获取上一周期的累计流量值
                double previousTotalFlow = GetTotalFlowFromSecondData(time.AddSeconds(-30), totalFlowNodeId);
                if(double.IsNaN(previousTotalFlow) || double.IsInfinity(previousTotalFlow))
                {
                    return 0;
                }

                // 计算差值
                double difference = currenTotalFlowValue - previousTotalFlow;

                // 如果差值为负数（可能是累计流量重置），返回0
                return difference >= 0 ? difference : 0;
            }
            catch
            {
                // 异常情况返回0
                return 0;
            }
        }

        /// <summary>
        /// 获取指定时刻的30秒数据类型的累计流量值
        /// </summary>
        /// <param name="dataTime">指定时间</param>
        /// <param name="totalFlowNodeId">累计流量因子ID</param>
        /// <returns>上一周期累计流量值</returns>
        private static double GetTotalFlowFromSecondData(DateTime dataTime, string totalFlowNodeId)
        {
            try
            {
                var previousTotalFlow = double.NaN;

                FpiTable table = FpiDataBase.GetInstance().FindTableByName(DbConfig.POLLUTION_MEASURE_DATA_TABLE);
                if(table != null)
                {
                    lock(lockObj)
                    {
                        SearchConditionCollection condition = new SearchConditionCollection
                    {
                        new SearchCondition("datatime", new ColumnComparison(SqlOperator.Equal, dataTime)),
                        new SearchCondition("datatype", new ColumnComparison(SqlOperator.Equal,(int)ePollutionDataType.实时数据))
                    };

                        var result = table.Search(condition);
                        if(result.Count > 0)
                        {
                            try
                            {
                                previousTotalFlow = Convert.ToSingle(result[0].GetFieldValue($"{DbConfig.PREFIX_F}{totalFlowNodeId}"));
                            }
                            catch
                            {
                                // ignore
                            }
                        }
                    }
                }

                return previousTotalFlow;
            }
            catch(Exception)
            {
                return double.NaN;
            }
        }

        #endregion

        #region 其他类型数据

        /// <summary>
        /// 插入计算后的污染源数据
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        private static void InsertCalculatedPollutionData(DateTime time, ePollutionDataType dataType)
        {
            try
            {
                // 获取所有待存储的因子
                var valueNodeList = ReportManager.GetInstance().GetFirstQueryGroup()?.GetQueryGroupValueNode();
                if(valueNodeList == null || valueNodeList.Count == 0) return;

                // 获取配置信息
                var config = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo;
                var calculateMethod = GetCalculateMethodForDataType(dataType, config);

                // 性能优化：批量预查询所有因子的历史数据
                // 需要预查询的情况：计算平均值时
                Dictionary<string, List<(double Value, int State, double TotalFlow, DateTime DataTime)>> batchHistoricalData = null;
                if(calculateMethod == eDataCalculateMethod.算数平均 || calculateMethod == eDataCalculateMethod.加权平均)
                {
                    batchHistoricalData = GetBatchValidDataInPeriod(valueNodeList, time, dataType);
                }

                var sb = new StringBuilder();
                sb.Append("insert into ").Append(DbConfig.POLLUTION_MEASURE_DATA_TABLE).Append($"(datatime,datatype");

                // 各统计量因子字段名
                foreach(var valueNode in valueNodeList)
                {
                    string valueNodeId = valueNode.id;
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId);
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId).Append(DbConfig.POSTFIX);
                }

                sb.Append($",{DbConfig.TOTAL_FLOW}) values('").Append(time.ToString(DbConfig.DATETIME_FORMAT)).Append("', ").Append((int)dataType);

                // 计算各因子数据
                foreach(var valueNode in valueNodeList)
                {
                    var calculatedData = CalculateFactorDataOptimized(valueNode, time, dataType, calculateMethod, batchHistoricalData);
                    sb.Append(", ").Append(calculatedData.Value.ToDBSaveFormat());
                    sb.Append(", ").Append(calculatedData.State);
                }

                // 计算时段流量
                double totalFlowValue = CalculatePeriodTotalFlow(time, dataType, config.TotalFlowNodeId);
                sb.Append(", ").Append(totalFlowValue.ToDBSaveFormat());

                sb.Append(")");
                DbAccess.ExecuteNonQuery(sb.ToString());
            }
            catch(Exception ex)
            {
                // 记录错误日志但不抛出异常，确保系统稳定运行
                // LogHelper.WriteErrorLog($"插入计算数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算因子数据
        /// </summary>
        /// <param name="valueNode">因子节点</param>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="calculateMethod">计算方法</param>
        /// <param name="batchHistoricalData">批量预查询的历史数据</param>
        /// <returns>计算结果</returns>
        private static (double Value, int State) CalculateFactorDataOptimized(ValueNode valueNode, DateTime time, ePollutionDataType dataType, eDataCalculateMethod calculateMethod, Dictionary<string, List<(double Value, int State, double TotalFlow, DateTime DataTime)>> batchHistoricalData)
        {
            try
            {
                // 累计流量因子特殊处理
                var totalFlowNodeId = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId;
                if(!string.IsNullOrEmpty(totalFlowNodeId) && valueNode.id == totalFlowNodeId)
                {
                    switch(calculateMethod)
                    {
                        case eDataCalculateMethod.实时值:
                            return (valueNode.GetValue(), valueNode.State);
                        case eDataCalculateMethod.算数平均:
                        case eDataCalculateMethod.加权平均:
                            return CalculateTotalFlowFactorDataOptimized(valueNode, batchHistoricalData);
                        default:
                            return (valueNode.GetValue(), valueNode.State);
                    }
                }

                // 常规因子处理
                switch(calculateMethod)
                {
                    case eDataCalculateMethod.实时值:
                        return (valueNode.GetValue(), valueNode.State);
                    case eDataCalculateMethod.算数平均:
                        return CalculateArithmeticAverageOptimized(valueNode, batchHistoricalData);
                    case eDataCalculateMethod.加权平均:
                        return CalculateWeightedAverageOptimized(valueNode, batchHistoricalData);
                    default:
                        return (valueNode.GetValue(), valueNode.State);
                }
            }
            catch(Exception)
            {
                return (double.NaN, (int)eValueNodeState.B);
            }
        }

        /// <summary>
        /// 从预查询数据中获取最新的累计流量值
        /// </summary>
        /// <param name="valueNode">累计流量因子节点</param>
        /// <param name="calculateMethod">计算方法</param>
        /// <param name="batchHistoricalData">批量预查询的历史数据</param>
        /// <returns>计算结果</returns>
        private static (double Value, int State) CalculateTotalFlowFactorDataOptimized(ValueNode valueNode, Dictionary<string, List<(double Value, int State, double TotalFlow, DateTime DataTime)>> batchHistoricalData)
        {
            try
            {
                double latestTotalFlowData = double.NaN;

                // 从预查询数据中获取该累计流量因子的数据
                if(batchHistoricalData != null && batchHistoricalData.TryGetValue(valueNode.id, out var validData) && validData.Count > 0)
                {
                    // 使用LINQ查找时间戳最大的数据记录
                    var latestData = validData.OrderByDescending(d => d.DataTime).First();
                    latestTotalFlowData = latestData.Value;
                }

                if(!double.IsNaN(latestTotalFlowData) && !double.IsInfinity(latestTotalFlowData))
                {
                    return (latestTotalFlowData, (int)eValueNodeState.N);
                }
                else
                {
                    return (double.NaN, (int)eValueNodeState.B);
                }
            }
            catch(Exception)
            {
                return (double.NaN, (int)eValueNodeState.B);
            }
        }

        /// <summary>
        /// 计算算数平均值
        /// </summary>
        /// <param name="valueNode">因子节点</param>
        /// <param name="batchHistoricalData">批量预查询的历史数据</param>
        /// <returns>计算结果</returns>
        private static (double Value, int State) CalculateArithmeticAverageOptimized(ValueNode valueNode, Dictionary<string, List<(double Value, int State, double TotalFlow, DateTime DataTime)>> batchHistoricalData)
        {
            try
            {
                // 从预查询数据中获取该因子的数据
                if(batchHistoricalData == null || !batchHistoricalData.TryGetValue(valueNode.id, out var validData) || validData.Count == 0)
                {
                    return (double.NaN, (int)eValueNodeState.B);
                }

                // 计算算数平均值
                double sum = validData.Sum(d => d.Value);
                double average = sum / validData.Count;

                // 取最频繁出现的数据标记
                int mostFrequentState = GetMostFrequentState(validData.Select(d => d.State));

                return (average, mostFrequentState);
            }
            catch(Exception)
            {
                return (double.NaN, (int)eValueNodeState.B);
            }
        }

        /// <summary>
        /// 计算加权平均值
        /// </summary>
        /// <param name="valueNode">因子节点</param>
        /// <param name="batchHistoricalData">批量预查询的历史数据</param>
        /// <returns>计算结果</returns>
        private static (double Value, int State) CalculateWeightedAverageOptimized(ValueNode valueNode, Dictionary<string, List<(double Value, int State, double TotalFlow, DateTime DataTime)>> batchHistoricalData)
        {
            try
            {
                // 从预查询数据中获取该因子的数据
                if(batchHistoricalData == null || !batchHistoricalData.TryGetValue(valueNode.id, out var validData) || validData.Count == 0)
                {
                    return (double.NaN, (int)eValueNodeState.B);
                }

                // 使用累计流量作为权重进行加权平均计算
                double weightedSum = 0;
                double totalWeight = 0;
                var validStates = new List<int>();

                foreach(var data in validData)
                {
                    double weight = data.TotalFlow;
                    if(double.IsNaN(weight) || weight <= 0)
                    {
                        continue; // 权重为null或0时丢弃该组数据
                    }

                    weightedSum += data.Value * weight;
                    totalWeight += weight;
                    validStates.Add(data.State);
                }

                if(totalWeight == 0 || validStates.Count == 0)
                {
                    return (double.NaN, (int)eValueNodeState.B);
                }

                double weightedAverage = weightedSum / totalWeight;
                int mostFrequentState = GetMostFrequentState(validStates);

                return (weightedAverage, mostFrequentState);
            }
            catch(Exception)
            {
                return (double.NaN, (int)eValueNodeState.B);
            }
        }

        /// <summary>
        /// 批量获取所有因子在指定时间段内的有效数据（性能优化版本）
        /// </summary>
        /// <param name="valueNodeList">因子节点列表</param>
        /// <param name="time">时间</param>
        /// <param name="dataType">要生成的数据类型</param>
        /// <returns>按因子ID分组的有效数据字典，包含时间戳信息</returns>
        private static Dictionary<string, List<(double Value, int State, double TotalFlow, DateTime DataTime)>> GetBatchValidDataInPeriod(List<ValueNode> valueNodeList, DateTime time, ePollutionDataType dataType)
        {
            var result = new Dictionary<string, List<(double Value, int State, double TotalFlow, DateTime DataTime)>>();

            // 查询数据时间范围
            var (startTime, endTime) = GetPeriodRange(time, dataType);

            // 待查询数据类型
            int queryDataType = GetQueryDataType(dataType);

            try
            {
                // 构建所有因子的字段列表
                var selectFields = new List<string>();
                selectFields.Add("datatime"); // 添加时间字段用于排序
                selectFields.Add(DbConfig.TOTAL_FLOW); // 添加累计流量字段

                foreach(var valueNode in valueNodeList)
                {
                    string valueField = $"{DbConfig.PREFIX_F}{valueNode.id}";
                    string stateField = $"{DbConfig.PREFIX_F}{valueNode.id}{DbConfig.POSTFIX}";
                    selectFields.Add(valueField);
                    selectFields.Add(stateField);
                }

                // 构建批量查询SQL
                string querySql = $"SELECT {string.Join(", ", selectFields)} FROM {DbConfig.POLLUTION_MEASURE_DATA_TABLE} WHERE datatype='{queryDataType}' AND datatime >='{startTime.ToString(DbConfig.DATETIME_FORMAT)}' AND datatime <='{endTime.ToString(DbConfig.DATETIME_FORMAT)}' ORDER BY datatime ASC";

                var dataTable = DbAccess.ExecuteQuery(querySql);
                if(dataTable != null && dataTable.Rows.Count > 0)
                {
                    // 初始化每个因子的数据列表
                    foreach(var valueNode in valueNodeList)
                    {
                        result[valueNode.id] = new List<(double Value, int State, double TotalFlow, DateTime DataTime)>();
                    }

                    // 遍历查询结果，按因子分组数据
                    foreach(DataRow row in dataTable.Rows)
                    {
                        double totalFlow = row[DbConfig.TOTAL_FLOW] != DBNull.Value ? Convert.ToDouble(row[DbConfig.TOTAL_FLOW]) : 0;
                        DateTime dataTime = Convert.ToDateTime(row["datatime"]);

                        foreach(var valueNode in valueNodeList)
                        {
                            string valueField = $"{DbConfig.PREFIX_F}{valueNode.id}";
                            string stateField = $"{DbConfig.PREFIX_F}{valueNode.id}{DbConfig.POSTFIX}";

                            if(row[valueField] != DBNull.Value &&
                                double.TryParse(row[valueField].ToString(), out double value) &&
                                !double.IsNaN(value) && !double.IsInfinity(value))
                            {
                                int state = row[stateField] != DBNull.Value ? Convert.ToInt32(row[stateField]) : (int)eValueNodeState.B;
                                result[valueNode.id].Add((value, state, totalFlow, dataTime));
                            }
                        }
                    }
                }
            }
            catch(Exception)
            {
                // 异常时返回空字典，各因子将使用空列表
                foreach(var valueNode in valueNodeList)
                {
                    result[valueNode.id] = new List<(double Value, int State, double TotalFlow, DateTime DataTime)>();
                }
            }

            return result;
        }

        /// <summary>
        /// 获取最频繁出现的状态
        /// </summary>
        /// <param name="states">状态列表</param>
        /// <returns>最频繁状态</returns>
        private static int GetMostFrequentState(IEnumerable<int> states)
        {
            if(states == null || !states.Any())
                return (int)eValueNodeState.B;

            return states.GroupBy(s => s)
                        .OrderByDescending(g => g.Count())
                        .First()
                        .Key;
        }

        /// <summary>
        /// 计算时段流量
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="totalFlowNodeId">累计流量因子编号</param>
        /// <returns>时段流量</returns>
        private static double CalculatePeriodTotalFlow(DateTime time, ePollutionDataType dataType, string totalFlowNodeId)
        {
            try
            {
                // 获取时段范围
                DateTime startTime = time;
                DateTime endTime;

                switch(dataType)
                {
                    case ePollutionDataType.分钟数据:
                        // 1点0分的数据应统计 1点0分0秒至1点1分0秒 的30秒数据
                        endTime = time.AddMinutes(1);
                        break;
                    case ePollutionDataType.十分钟数据:
                        // 1点0分的数据应统计 1点0分0秒至1点10分0秒 的30秒数据
                        endTime = time.AddMinutes(10);
                        break;
                    case ePollutionDataType.小时数据:
                        // 1点的数据应统计 1点0分至2点0分 的10分钟数据
                        endTime = time.AddHours(1);
                        break;
                    case ePollutionDataType.日数据:
                        // 12日的数据应统计 12日0点至13日0点 的小时数据
                        endTime = time.AddDays(1);
                        break;
                    default:
                        endTime = time.AddMinutes(1);
                        break;
                }

                // 使用SQL直接计算时段内首尾数据的累积流量差值
                return CalculatePeriodTotalFlowBySql(startTime, endTime, totalFlowNodeId);
            }
            catch(Exception)
            {
                return 0;
            }
        }

        /// <summary>
        /// 使用SQL语句直接计算指定时间段内首尾数据的累积流量差值（含头尾）
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="totalFlowNodeId">累计流量因子编号</param>
        /// <returns>累积流量差值</returns>
        private static double CalculatePeriodTotalFlowBySql(DateTime startTime, DateTime endTime, string totalFlowNodeId)
        {
            try
            {
                // 根据头标法规则构建时间条件
                string timeCondition = $"datatime >= '{startTime.ToString(DbConfig.DATETIME_FORMAT)}' AND datatime <= '{endTime.ToString(DbConfig.DATETIME_FORMAT)}'";

                // 累计流量因子字段名
                string valueField = $"{DbConfig.PREFIX_F}{totalFlowNodeId}";

                // 构建SQL查询语句，获取时间段内第一条和最后一条数据的累积流量值差值
                string querySql = $@"
                    SELECT
                        (SELECT {valueField}
                         FROM {DbConfig.POLLUTION_MEASURE_DATA_TABLE}
                         WHERE datatype='{(int)ePollutionDataType.实时数据}' AND {timeCondition}
                         ORDER BY datatime ASC
                         LIMIT 1) AS start_flow,
                        (SELECT {valueField}
                         FROM {DbConfig.POLLUTION_MEASURE_DATA_TABLE}
                         WHERE datatype='{(int)ePollutionDataType.实时数据}' AND {timeCondition}
                         ORDER BY datatime DESC
                         LIMIT 1) AS end_flow";

                var dataTable = DbAccess.ExecuteQuery(querySql);
                if(dataTable != null && dataTable.Rows.Count > 0)
                {
                    var row = dataTable.Rows[0];

                    // 获取开始和结束时间的累积流量值
                    double startFlow = double.NaN;
                    double endFlow = double.NaN;

                    if(row["start_flow"] != DBNull.Value &&
                       double.TryParse(row["start_flow"].ToString(), out startFlow))
                    {
                        // 开始流量值有效
                    }
                    else
                    {
                        startFlow = double.NaN;
                    }

                    if(row["end_flow"] != DBNull.Value &&
                       double.TryParse(row["end_flow"].ToString(), out endFlow))
                    {
                        // 结束流量值有效
                    }
                    else
                    {
                        endFlow = double.NaN;
                    }

                    // 检查数据有效性
                    if(double.IsNaN(startFlow) || double.IsNaN(endFlow) ||
                       double.IsInfinity(startFlow) || double.IsInfinity(endFlow))
                    {
                        return 0;
                    }

                    // 计算差值
                    double difference = endFlow - startFlow;

                    // 如果差值为负数（可能是累计流量重置），返回0
                    return difference >= 0 ? difference : 0;
                }

                // 无数据时返回0
                return 0;
            }
            catch(Exception)
            {
                // 异常情况返回0
                return 0;
            }
        }

        #endregion

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检查数据是否已存在
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        /// <returns>是否存在</returns>
        public static bool IsDataExists(DateTime time, ePollutionDataType dataType)
        {
            try
            {
                string searchSql = $"select count(*) from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime='{time.ToString(DbConfig.DATETIME_FORMAT)}' and datatype='{(int)dataType}'";
                int count = DbAccess.QueryRecordCount(searchSql);
                return count > 0;
            }
            catch(Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取数据类型对应的计算方法
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="config">配置信息</param>
        /// <returns>计算方法</returns>
        private static eDataCalculateMethod GetCalculateMethodForDataType(ePollutionDataType dataType, PollutionDataSaveConfig config)
        {
            switch(dataType)
            {
                case ePollutionDataType.分钟数据:
                case ePollutionDataType.十分钟数据:
                case ePollutionDataType.小时数据:
                    return config.MinuteDataCalculateMethod;
                case ePollutionDataType.日数据:
                    return config.DayDataCalculateMethod;
                default:
                    return eDataCalculateMethod.实时值; // 默认实时值
            }
        }

        /// <summary>
        /// 获取计算当前数据类型时所用数据时段
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        /// <returns>开始时间和结束时间</returns>
        private static (DateTime StartTime, DateTime EndTime) GetPeriodRange(DateTime time, ePollutionDataType dataType)
        {
            switch(dataType)
            {
                case ePollutionDataType.分钟数据:
                    // 1点0分的数据应统计 1点0分0秒至1点1分0秒 的30秒数据（不含头1点0分0秒，含尾1点1分0秒）
                    return (time.AddSeconds(1), time.AddMinutes(1));
                case ePollutionDataType.十分钟数据:
                    // 1点0分的数据应统计 1点0分0秒至1点10分0秒 的30秒数据（不含头1点0分0秒，含尾1点10分0秒）
                    return (time.AddSeconds(1), time.AddMinutes(10));
                case ePollutionDataType.小时数据:
                    // 1点的数据应统计 1点0分至2点0分 的10分钟数据（含头1点0分，不含尾2点0分）
                    return (time, time.AddHours(1).AddSeconds(-1));
                case ePollutionDataType.日数据:
                    // 12日的数据应统计 12日0点至13日0点 的小时数据（含头12日0点，不含尾13日0点）
                    return (time, time.AddDays(1).AddSeconds(-1));
                default:
                    return (time, time);
            }
        }

        /// <summary>
        /// 获取计算当前数据类型时应查询的源数据类型
        /// </summary>
        /// <param name="dataType"></param>
        /// <returns></returns>
        private static int GetQueryDataType(ePollutionDataType dataType)
        {
            // 待查询数据类型
            int queryDataType;
            switch(dataType)
            {
                case ePollutionDataType.分钟数据:
                case ePollutionDataType.十分钟数据:
                    queryDataType = (int)ePollutionDataType.实时数据; // 计算分钟或十分钟数据时，查询实时数据
                    break;
                case ePollutionDataType.小时数据:
                    queryDataType = (int)ePollutionDataType.十分钟数据; // 计算小时数据时，查询十分钟数据
                    break;
                case ePollutionDataType.日数据:
                    queryDataType = (int)ePollutionDataType.小时数据; // 计算日数据时，查询小时数据
                    break;
                default:
                    queryDataType = (int)ePollutionDataType.实时数据;
                    break;
            }

            return queryDataType;
        }

        /// <summary>
        /// 转换为数据库存储格式
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private static string ToDBSaveFormat(this double value)
        {
            return (double.IsNaN(value) || double.IsInfinity(value)) ? "null" : value.ToString("G10");
        }

        #endregion
    }
}